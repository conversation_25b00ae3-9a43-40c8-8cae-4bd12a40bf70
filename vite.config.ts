import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import electron from 'vite-plugin-electron';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    electron({
      entry: [
        'electron/src/main.ts',
        'electron/src/preload.ts'
      ],
    }),
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, './renderer/src'),
      '@shared': resolve(__dirname, './shared'),
    },
  },
  build: {
    outDir: 'dist-electron/renderer',
    emptyOutDir: true,
  },
  server: {
    host: '127.0.0.1',
  },
  clearScreen: false,
});