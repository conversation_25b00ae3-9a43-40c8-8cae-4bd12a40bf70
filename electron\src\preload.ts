import { contextBridge, ipcRenderer } from 'electron';

// 安全地暴露 Electron API 给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 窗口控制
  minimizeWindow: () => ipcRenderer.invoke('window:minimize'),
  maximizeWindow: () => ipcRenderer.invoke('window:maximize'),
  closeWindow: () => ipcRenderer.invoke('window:close'),
  
  // 文件对话框
  openFile: () => ipcRenderer.invoke('dialog:openFile'),
  
  // 系统信息
  platform: process.platform,
});

// 禁用 Node.js 集成后，可以通过上下文桥接安全地暴露必要的 API
// 注意：不要暴露任何可能导致安全风险的 API