---
type: 'manual'
---

# React组件重构快速参考指南

## 🚀 快速决策表

| 组件特征           | 重构策略          | 优先级 |
| ------------------ | ----------------- | ------ |
| < 50行，单一功能   | 提取工具函数      | 低     |
| 50-100行，中等复杂 | 状态逻辑分离      | 中     |
| 100-200行，多功能  | 状态+业务逻辑分离 | 高     |
| > 200行，超复杂    | 全面重构+组件化   | 紧急   |

## 📋 重构检查清单

### ✅ 开始重构前

- [ ] 组件代码行数 > 100行
- [ ] useState 使用 > 5个
- [ ] 包含复杂业务逻辑
- [ ] 难以进行单元测试
- [ ] 修改功能需要改动多处

### ✅ 状态管理Hook设计

- [ ] 只包含状态定义和基础操作
- [ ] 派生状态通过计算得出
- [ ] 提供清理和重置函数
- [ ] 不包含业务逻辑和API调用

### ✅ 业务逻辑Hook设计

- [ ] 函数接收参数而非直接访问状态
- [ ] 统一的错误处理策略
- [ ] 通过回调与状态Hook协作
- [ ] 可以独立测试

### ✅ 子组件抽离

- [ ] 功能独立且完整
- [ ] JSX结构 > 20行
- [ ] 有复用价值或潜力
- [ ] Props设计合理

### ✅ 重构完成验证

- [ ] 功能完全一致
- [ ] 代码行数减少30%+
- [ ] 可以独立测试
- [ ] 性能无明显下降

## 🎯 命名规范

### Hook命名

```typescript
// 状态管理Hook
const useComponentNameState = () => {
  /* ... */
};

// 业务逻辑Hook
const useComponentNameLogic = () => {
  /* ... */
};
const useFeatureNameLogic = () => {
  /* ... */
};

// 工具Hook
const useFormValidation = () => {
  /* ... */
};
const useApiRequest = () => {
  /* ... */
};
```

### 组件命名

```typescript
// 主组件
const ComponentName = () => {
  /* ... */
};

// 子组件 - 功能描述
const UnboundForm = () => {
  /* ... */
};
const BoundInfo = () => {
  /* ... */
};

// 子组件 - 通用组件
const FormInput = () => {
  /* ... */
};
const ActionButton = () => {
  /* ... */
};
```

## 🔧 常用重构模式

### 模式1：状态逻辑分离

```typescript
// Before
const Component = () => {
  const [state1, setState1] = useState();
  const [state2, setState2] = useState();
  // 业务逻辑...
};

// After
const useComponentState = () => {
  const [state1, setState1] = useState();
  const [state2, setState2] = useState();
  return { state1, setState1, state2, setState2 };
};

const Component = () => {
  const { state1, setState1, state2, setState2 } = useComponentState();
  // 只保留UI逻辑...
};
```

### 模式2：业务逻辑分离

```typescript
// Before
const Component = () => {
  const handleSubmit = async () => {
    try {
      await api.submit();
      // 处理成功...
    } catch (error) {
      // 处理错误...
    }
  };
};

// After
const useComponentLogic = () => {
  const handleSubmit = useCallback(async (data, onSuccess, onError) => {
    try {
      await api.submit(data);
      onSuccess?.();
    } catch (error) {
      onError?.(error);
    }
  }, []);

  return { handleSubmit };
};
```

### 模式3：子组件抽离

```typescript
// Before
const Component = () => {
  return (
    <div>
      {/* 大量JSX */}
      <div className="form-section">
        {/* 表单相关JSX */}
      </div>
      <div className="info-section">
        {/* 信息展示相关JSX */}
      </div>
    </div>
  );
};

// After
const FormSection = ({ data, onChange }) => {
  return <div className="form-section">{/* 表单JSX */}</div>;
};

const InfoSection = ({ info }) => {
  return <div className="info-section">{/* 信息JSX */}</div>;
};

const Component = () => {
  return (
    <div>
      <FormSection data={data} onChange={handleChange} />
      <InfoSection info={info} />
    </div>
  );
};
```

## ⚠️ 常见陷阱

### ❌ 避免的做法

```typescript
// 1. 状态Hook中包含业务逻辑
const useComponentState = () => {
  const [data, setData] = useState();

  // ❌ 不应该在状态Hook中调用API
  const fetchData = async () => {
    const result = await api.getData();
    setData(result);
  };

  return { data, fetchData };
};

// 2. 过度抽象
const useGenericProcessor = (data, config, handlers) => {
  // ❌ 过于通用，难以理解和维护
};

// 3. Props传递过多
interface ComponentProps {
  // ❌ 传递整个状态对象
  state: ComponentState;
  // ❌ 传递不相关的数据
  appConfig: AppConfig;
  userInfo: UserInfo;
}
```

### ✅ 推荐的做法

```typescript
// 1. 清晰的职责分离
const useComponentState = () => {
  // 只管理状态
  const [data, setData] = useState();
  return { data, setData };
};

const useComponentLogic = () => {
  // 只处理业务逻辑
  const fetchData = useCallback(async (onSuccess) => {
    const result = await api.getData();
    onSuccess(result);
  }, []);

  return { fetchData };
};

// 2. 合适的抽象层次
const useFormValidation = (rules) => {
  // 专注于表单验证这一特定领域
};

// 3. 最小化Props
interface ComponentProps {
  // 只传递必需的数据
  data: ComponentData;
  onSubmit: (data: ComponentData) => void;
}
```

## 📊 重构效果评估

### 成功指标

- ✅ 主组件代码减少30-60%
- ✅ 每个Hook/组件职责单一
- ✅ 可以独立编写单元测试
- ✅ 新功能开发效率提升
- ✅ Bug修复范围更精确

### 失败信号

- ❌ 重构后代码更难理解
- ❌ 性能明显下降
- ❌ 测试变得更复杂
- ❌ 开发效率降低
- ❌ 过度抽象导致维护困难

## 🎓 学习资源

### 推荐阅读

1. React官方文档 - Custom Hooks
2. 《Clean Code》- Robert C. Martin
3. 《Refactoring》- Martin Fowler
