---
type: 'always_apply'
---

# 在编写React组件时，必须严格遵循状态与逻辑分离（State-Logic Separation）的设计原则，具体要求如下：

1. **状态管理分离**：

   - 将组件的状态逻辑提取到自定义Hook中（如useXxxState、useXxxLogic）
   - 组件本身只负责UI渲染和事件绑定
   - 避免在组件内部直接使用useState、useEffect等状态管理Hook

2. **业务逻辑分离**：

   - 将复杂的业务逻辑、数据处理、API调用等提取到独立的Hook或工具函数中
   - 组件不应包含具体的业务规则实现
   - 使用自定义Hook封装相关的状态和操作方法

3. **代码组织结构**：

   - 组件文件：仅包含JSX结构和基本的事件处理
   - Hook文件：包含状态定义、状态更新逻辑、副作用处理
   - 工具函数：包含纯函数形式的业务逻辑

4. **命名规范**：
   - 状态Hook：use[ComponentName]State
   - 逻辑Hook：use[ComponentName]Logic 或 use[FeatureName]
   - 确保Hook名称能清晰表达其职责

这种分离方式能够显著提高代码的可维护性、可复用性和可测试性，便于单元测试和逻辑复用。
