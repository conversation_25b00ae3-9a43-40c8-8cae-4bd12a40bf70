---
type: "manual"
---

# 回答开始时说:"您好，我即将实现 Figma UI！！！"

# Figma 设计稿严格实现规则

## 核心原则

**严格按照 Figma 设计稿实现 UI 内容，不允许任何主观修改或"优化"**
**理解用户需求，根据用户需要执行对应功能**

## MCP Server 工具：

- get_variable_defs：Returns variables and styles used in your selection—like colors, spacing, and typography.
- get_code：Use this to generate code for your Figma selection using the MCP server. The default output is React + Tailwind, but you can customize this through your prompts
- get_code_connect_map：Retrieves a mapping between Figma node IDs and their corresponding code components in your codebase. Specifically, it returns an object where each key is a Figma node ID, and the value contains.
- get_image：This takes a screenshot of your selection to preserve layout fidelity. Keep this on unless you’re managing token limits.

## 实施流程

### 0. 根据用户传入的 figma 链接分析`node-id`, 精确获得内容。

### 1. 获取设计稿信息

在实现任何 UI 相关功能前，必须参考以下内容，调用工具：

```javascript
// 1. 获取设计稿图片
get_image({
  nodeId: "设计稿节点ID",
  clientName: "claude desktop",
  clientLanguages: "javascript,html,css",
  clientFrameworks: "根据项目框架信息填写", // vue/react
});

// 2. 获取设计稿代码
get_code({
  nodeId: "设计稿节点ID",
  clientName: "claude desktop",
  clientLanguages: "javascript,html,css",
  clientFrameworks: "根据项目框架信息填写", // vue/react
});

// get_variable_defs
```

### 2. 样式属性严格对照

#### 重复样式

- 重复样式使用 css 变量统一处理，避免过多样式重复书写

#### 文本样式

- `leading-[normal]` → `line-height: normal`
- `leading-[1.2]` → `line-height: 1.2`
- `text-[16px]` → `font-size: 32rpx` (uniapp 中需要转换为 rpx)
- `font-['PingFang_SC:Semibold']` → `font-weight: 600`

#### 颜色

- `text-[#131313]` → `color: #131313`
- `bg-[#ffffff]` → `background-color: #ffffff`

#### 间距和尺寸

- `w-[351px]` → `width: 702rpx` (px 转 rpx: px \* 2), 如果是子元素，则继承宽度
- `h-[62px]` → `height: 124rpx`
- `left-3` → `margin-left: 24rpx` (Tailwind 单位转换)

#### 圆角

- `rounded-xl` → `border-radius: 24rpx`

### 3. 当需要从 Figma 设计文件中获取设计资源时，流程如下：

1. 使用 get_image 工具从 Figma 链接获取设计稿的图片信息
2. 使用 get_code 工具获取设计稿中图标的代码实现
3. 从获取的 SVG 代码中提取对应的独立的图标（名称按照项目内容命名, 或者按照 figma 内容进行命名）
4. 使用 Write 工具将每个图标分别保存为独立的 SVG 文件到，项目中资源存放适当的目录位置

注意：处理 Figma 资源时应保持原始设计的视觉特性，包括渐变、阴影和其他效果。

#### 5. UniApp/Taro 样式转换

```scss
// Figma: text-[16px]
font-size: 32rpx; // px * 2 = rpx

// Figma: leading-[normal]
line-height: normal; // 严格使用normal，不使用数值

// Figma: w-[351px]
width: 702rpx; // px * 2 = rpx
```

#### 6. 组件结构对照

```html
<!-- Figma设计稿结构 -->
<div className="stats-item">
  <div className="stats-number">16</div>
  <div className="stats-label">优惠券</div>
</div>

<!-- UniApp实现 -->
<view class="stats-item">
  <view class="stats-number">16</view>
  <view class="stats-label">优惠券</view>
</view>

<!-- Taro实现 -->
<View className="bind-mall-account">
  <View class="stats-number">16</View>
  <View class="stats-label">优惠券</View>
</View>
```

## 禁止行为

### ❌ 绝对禁止

1. **随意修改设计稿中的样式值**

   - 不允许将 `line-height: normal` 改为 `line-height: 1.2`
   - 不允许调整设计稿中明确指定的颜色值
   - 不允许修改字体大小、间距等数值

2. **添加未在设计稿中体现的样式**

   - 不添加额外的阴影、边框等效果
   - 不添加动画效果（除非设计稿明确要求）

3. **忽略设计稿细节**
   - 必须检查每个元素的精确位置
   - 必须遵循设计稿的层级结构

### ✅ 必须执行

1. **获取设计稿信息**

   - 每次 UI 实现前必须调用 Figma 工具
   - 仔细分析设计稿的每个细节

2. **精确转换**

   - 严格按照转换规则进行样式转换
   - 保持设计稿的视觉效果完全一致

3. **验证实现**
   - 实现后对比设计稿检查差异
   - 确保视觉效果 100%匹配

## 项目结构遵循

### 样式文件组织

```
pages/
├── user/
│   ├── index.vue          # 用户中心主页
│   └── index_new.vue      # 新版用户中心
├── goods/
│   ├── user_goods_collection/
│   └── browsing_history/
└── merchant/
    └── user_integral/
```

### 样式实现模式(Vue)

```vue
<template>
  <view class="user-stats-section">
    <view class="stats-item" @click="goToCoupon">
      <view class="stats-number">{{ userStats.couponCount }}</view>
      <view class="stats-label">优惠券</view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.user-stats-section {
  // 严格按照Figma设计稿实现
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 24rpx;
  // ... 其他样式必须与设计稿一致
}

.stats-item {
  .stats-number {
    font-size: 32rpx;
    font-weight: 600;
    color: #131313;
    line-height: normal; // 严格遵循设计稿
  }

  .stats-label {
    font-size: 20rpx;
    color: #131313;
    line-height: normal; // 严格遵循设计稿
  }
}
</style>
```

## 检查清单

实现 UI 前必须确认：

- [ ] 已获取 Figma 设计稿信息
- [ ] 已分析所有样式属性
- [ ] 已按照转换规则进行样式转换
- [ ] 未添加任何主观"优化"
- [ ] 视觉效果与设计稿 100%匹配
- [ ] 查看当前项目的基本信息，根据项目使用符合项目的代码实现

## 违规处理

如发现违反此规则的实现：

1. 立即停止当前实现
2. 重新获取设计稿信息
3. 严格按照规则重新实现
4. 确保完全符合设计稿要求

---

**记住：设计师的每一个像素都有其意义，我们的职责是 100%还原设计意图。**
