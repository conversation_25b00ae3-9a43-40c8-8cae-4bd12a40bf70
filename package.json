{"name": "fast-ocr", "version": "1.0.0", "description": "一个基于 Electron + React + TypeScript + Vite 的现代化 OCR（光学字符识别）桌面应用程序", "main": "dist-electron/main/main.js", "scripts": {"dev": "vite", "build": "tsc && vite build && electron-builder", "start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "clean": "rimraf dist-electron"}, "keywords": ["electron", "react", "typescript", "vite", "ocr"], "author": "jf", "license": "MIT", "devDependencies": {"@electron-forge/cli": "^7.2.0", "@electron-forge/maker-deb": "^7.2.0", "@electron-forge/maker-rpm": "^7.2.0", "@electron-forge/maker-squirrel": "^7.2.0", "@electron-forge/maker-zip": "^7.2.0", "@types/node": "^20.10.4", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "@vitejs/plugin-react": "^4.2.1", "electron": "^37.0.0", "electron-builder": "^24.9.1", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "rimraf": "^5.0.5", "typescript": "^5.8.0", "vite": "^7.0.0", "vite-plugin-electron": "^0.28.0"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}}