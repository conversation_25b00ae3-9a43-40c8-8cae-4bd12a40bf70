---
type: "manual"
---

请为当前工作目录中的Git仓库生成一个规范的commit消息。具体要求：

1. 首先检查当前Git仓库的状态，包括：
   - 查看已暂存的文件变更（git diff --cached）
   - 查看未暂存的文件变更（git diff）
   - 查看新增的未跟踪文件（git status）

2. 基于检测到的变更内容，生成一个符合以下规范的commit消息：
   - 使用中文描述
   - 遵循约定式提交（Conventional Commits）格式：`类型(范围): 描述`
   - 常用类型包括：feat（新功能）、fix（修复）、docs（文档）、style（样式）、refactor（重构）、test（测试）、chore（构建/工具）
   - 描述应简洁明了，说明本次提交的主要变更内容
   - 如果有多个重要变更，提供简短的详细说明
   - 仔细查看文件变更，按照重点修改从上到下排列

3. 如果当前没有任何变更需要提交，请说明当前仓库状态并提供相应建议。

请不要实际执行git commit命令，只生成commit消息供我确认。