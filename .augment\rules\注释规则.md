---
type: 'manual'
---

重新生成完整的 JSDoc 注释，特别是 `@returns` 部分。请严格按照以下格式要求：

1. **整体结构**：保持现有的函数描述不变，只修改 `@returns` 部分
2. **@returns 格式要求**：
   - 第一行：`@returns 返回对象包含以下属性：`（不包含类型定义）
   - 后续每行：`- *属性名* \`类型\` - 属性说明`
   - 属性名使用斜体格式（*属性名*）
   - 类型使用代码格式（\`类型\`），不使用花括号
   - 每行以 `*` 开头，然后是一个空格，再是 `-`
3. **示例格式**：
```
 * @returns 返回对象包含以下属性：
 * - *phone* `string` - 用户输入的手机号码
 * - *code* `string` - 用户输入的验证码
```

请确保格式完全一致，包括缩进、空格和标点符号。